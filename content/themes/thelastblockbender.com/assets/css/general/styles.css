/* dark mode */
body.darkMode {
background-color:hsla(265,70%,3%,1);
background-image:
radial-gradient(at 98% 26%, hsla(272,50%,10%,1) 0px, transparent 50%),
radial-gradient(at 4% 22%, hsla(272,50%,10%,1) 0px, transparent 50%);
color: var(--white-color);
}

.darkMode .gh-head {
background: transparent;
color: var(--white-color);
}

.darkMode a {
color: var(--white-color);	
}

.darkMode h1, .darkMode h2, .darkMode h3, .darkMode h4, .darkMode h5, .darkMode h6 {
color: var(--white-color);
}


.darkMode .kg-callout-card-grey {
    background: #77797e;
}

.darkMode .kg-callout-card-white {
	color: var(--black-color);
}

.darkMode .kg-callout-card-blue {
    background: #8492c3;
    color: var(--black-color);
}

.darkMode .kg-callout-card-green {
    background: #84c3b0;
    color: var(--black-color);
}

.darkMode .kg-callout-card-yellow {
    background: #c3bc84;
    color: var(--black-color);
}

.darkMode .kg-callout-card-red {
    background: #c39184;
    color: var(--black-color);
}

.darkMode .kg-callout-card-pink {
    background: #c38484;
    color: var(--black-color);
}

.darkMode .kg-callout-card-purple {
    background: #9d84c3;
    color: var(--black-color);
}


/* wide template */

body.wideTemplate .gh-canvas {
    --content-width: min(70vw, 1260px);
}
@media (max-width: 1024px) {
	body.wideTemplate .gh-canvas {
    --content-width: 100vw;
}	
}


/* Global Style changes */
* {
	transition: all 0.2s ease;
}